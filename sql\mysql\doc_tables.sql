-- =============================================
-- 文档管理平台数据表结构
-- 基于芋道管理系统扩展
-- =============================================

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 1. 文档表 (doc_document)
-- 存储文档的基本信息，通过file_id关联现有的infra_file表
CREATE TABLE `doc_document` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '文档ID',
  `file_id` bigint COMMENT '关联文件ID（infra_file表）',
  `title` varchar(200) NOT NULL COMMENT '文档标题',
  `content` longtext COMMENT '文档内容（Markdown/纯文本）',
  `content_type` tinyint NOT NULL DEFAULT '1' COMMENT '内容类型：1-Markdown 2-富文本 3-Office文档',
  `summary` varchar(500) COMMENT '文档摘要',
  `cover_url` varchar(500) COMMENT '封面图片URL',
  `category_id` bigint COMMENT '分类ID',
  `tags` varchar(500) COMMENT '标签，逗号分隔',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：1-草稿 2-发布 3-归档 4-删除',
  `visibility` tinyint NOT NULL DEFAULT '1' COMMENT '可见性：1-公开 2-团队 3-私有',
  `word_count` int DEFAULT '0' COMMENT '字数统计',
  `read_count` int DEFAULT '0' COMMENT '阅读次数',
  `like_count` int DEFAULT '0' COMMENT '点赞次数',
  `comment_count` int DEFAULT '0' COMMENT '评论次数',
  `version` int NOT NULL DEFAULT '1' COMMENT '版本号',
  `creator_id` bigint NOT NULL COMMENT '创建者ID',
  `last_editor_id` bigint COMMENT '最后编辑者ID',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `publish_time` datetime COMMENT '发布时间',
  `tenant_id` bigint NOT NULL COMMENT '租户ID',
  `deleted` bit NOT NULL DEFAULT b'0' COMMENT '是否删除',
  PRIMARY KEY (`id`),
  KEY `idx_file_id` (`file_id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_creator_id` (`creator_id`),
  KEY `idx_status` (`status`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_doc_file` FOREIGN KEY (`file_id`) REFERENCES `infra_file` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文档表';

-- 2. 文档分类表 (doc_category)
-- 管理文档的层级分类结构，支持无限层级
CREATE TABLE `doc_category` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `parent_id` bigint NOT NULL DEFAULT '0' COMMENT '父分类ID，0表示根分类',
  `name` varchar(100) NOT NULL COMMENT '分类名称',
  `description` varchar(500) COMMENT '分类描述',
  `icon` varchar(100) COMMENT '分类图标',
  `sort` int NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：1-启用 2-禁用',
  `creator_id` bigint NOT NULL COMMENT '创建者ID',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `tenant_id` bigint NOT NULL COMMENT '租户ID',
  `deleted` bit NOT NULL DEFAULT b'0' COMMENT '是否删除',
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_sort` (`sort`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文档分类表';

-- 3. 文档标签表 (doc_tag)
-- 管理文档标签，支持标签统计和热门标签分析
CREATE TABLE `doc_tag` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '标签ID',
  `name` varchar(50) NOT NULL COMMENT '标签名称',
  `color` varchar(20) COMMENT '标签颜色',
  `description` varchar(200) COMMENT '标签描述',
  `use_count` int NOT NULL DEFAULT '0' COMMENT '使用次数',
  `creator_id` bigint NOT NULL COMMENT '创建者ID',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `tenant_id` bigint NOT NULL COMMENT '租户ID',
  `deleted` bit NOT NULL DEFAULT b'0' COMMENT '是否删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name_tenant` (`name`, `tenant_id`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_use_count` (`use_count`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文档标签表';

-- 4. 文档标签关联表 (doc_document_tag)
-- 文档与标签的多对多关联关系
CREATE TABLE `doc_document_tag` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '关联ID',
  `document_id` bigint NOT NULL COMMENT '文档ID',
  `tag_id` bigint NOT NULL COMMENT '标签ID',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `tenant_id` bigint NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_doc_tag` (`document_id`, `tag_id`),
  KEY `idx_document_id` (`document_id`),
  KEY `idx_tag_id` (`tag_id`),
  KEY `idx_tenant_id` (`tenant_id`),
  CONSTRAINT `fk_doc_tag_document` FOREIGN KEY (`document_id`) REFERENCES `doc_document` (`id`),
  CONSTRAINT `fk_doc_tag_tag` FOREIGN KEY (`tag_id`) REFERENCES `doc_tag` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文档标签关联表';

-- 5. 文档权限表 (doc_permission)
-- 管理文档的访问权限，支持用户、角色、部门三种权限类型
CREATE TABLE `doc_permission` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '权限ID',
  `document_id` bigint COMMENT '文档ID，为空表示分类权限',
  `category_id` bigint COMMENT '分类ID，为空表示文档权限',
  `permission_type` tinyint NOT NULL COMMENT '权限类型：1-用户 2-角色 3-部门',
  `permission_target` bigint NOT NULL COMMENT '权限目标ID（用户ID/角色ID/部门ID）',
  `permission_level` tinyint NOT NULL COMMENT '权限级别：1-查看 2-编辑 3-管理 4-删除',
  `creator_id` bigint NOT NULL COMMENT '创建者ID',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `tenant_id` bigint NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `idx_document_id` (`document_id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_permission_target` (`permission_target`),
  KEY `idx_tenant_id` (`tenant_id`),
  CONSTRAINT `fk_doc_permission_document` FOREIGN KEY (`document_id`) REFERENCES `doc_document` (`id`),
  CONSTRAINT `fk_doc_permission_category` FOREIGN KEY (`category_id`) REFERENCES `doc_category` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文档权限表';

-- 6. 文档版本表 (doc_version)
-- 记录文档的版本历史，支持版本对比和回滚
CREATE TABLE `doc_version` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '版本ID',
  `document_id` bigint NOT NULL COMMENT '文档ID',
  `version_number` int NOT NULL COMMENT '版本号',
  `title` varchar(200) NOT NULL COMMENT '版本标题',
  `content` longtext COMMENT '版本内容',
  `file_id` bigint COMMENT '版本文件ID',
  `change_log` varchar(1000) COMMENT '变更日志',
  `creator_id` bigint NOT NULL COMMENT '创建者ID',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `tenant_id` bigint NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `idx_document_id` (`document_id`),
  KEY `idx_version_number` (`version_number`),
  KEY `idx_tenant_id` (`tenant_id`),
  CONSTRAINT `fk_doc_version_document` FOREIGN KEY (`document_id`) REFERENCES `doc_document` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文档版本表';

-- 7. 文档评论表 (doc_comment)
-- 存储文档的评论和回复，支持层级评论结构
CREATE TABLE `doc_comment` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '评论ID',
  `document_id` bigint NOT NULL COMMENT '文档ID',
  `parent_id` bigint DEFAULT '0' COMMENT '父评论ID，0表示顶级评论',
  `content` text NOT NULL COMMENT '评论内容',
  `creator_id` bigint NOT NULL COMMENT '评论者ID',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `tenant_id` bigint NOT NULL COMMENT '租户ID',
  `deleted` bit NOT NULL DEFAULT b'0' COMMENT '是否删除',
  PRIMARY KEY (`id`),
  KEY `idx_document_id` (`document_id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_creator_id` (`creator_id`),
  KEY `idx_tenant_id` (`tenant_id`),
  CONSTRAINT `fk_doc_comment_document` FOREIGN KEY (`document_id`) REFERENCES `doc_document` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文档评论表';

-- 8. 文档访问日志表 (doc_access_log)
-- 记录文档的访问行为，用于统计分析和行为追踪
CREATE TABLE `doc_access_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `document_id` bigint NOT NULL COMMENT '文档ID',
  `user_id` bigint NOT NULL COMMENT '访问用户ID',
  `access_type` tinyint NOT NULL COMMENT '访问类型：1-查看 2-下载 3-编辑 4-分享',
  `ip_address` varchar(50) COMMENT 'IP地址',
  `user_agent` varchar(500) COMMENT '用户代理',
  `access_time` datetime NOT NULL COMMENT '访问时间',
  `tenant_id` bigint NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `idx_document_id` (`document_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_access_time` (`access_time`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文档访问日志表';

-- 9. OnlyOffice配置表 (doc_onlyoffice_config)
-- 存储OnlyOffice文档的配置信息，包括文档密钥、回调URL等
CREATE TABLE `doc_onlyoffice_config` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `document_id` bigint NOT NULL COMMENT '文档ID',
  `document_key` varchar(100) NOT NULL COMMENT '文档密钥',
  `callback_url` varchar(500) COMMENT '回调URL',
  `editor_config` text COMMENT '编辑器配置JSON',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：1-活跃 2-已关闭',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `tenant_id` bigint NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_document_key` (`document_key`),
  KEY `idx_document_id` (`document_id`),
  KEY `idx_tenant_id` (`tenant_id`),
  CONSTRAINT `fk_doc_onlyoffice_document` FOREIGN KEY (`document_id`) REFERENCES `doc_document` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='OnlyOffice配置表';

-- 10. 文档收藏表 (doc_favorite)
-- 用户收藏的文档
CREATE TABLE `doc_favorite` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '收藏ID',
  `document_id` bigint NOT NULL COMMENT '文档ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `create_time` datetime NOT NULL COMMENT '收藏时间',
  `tenant_id` bigint NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_doc_user` (`document_id`, `user_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_tenant_id` (`tenant_id`),
  CONSTRAINT `fk_doc_favorite_document` FOREIGN KEY (`document_id`) REFERENCES `doc_document` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文档收藏表';

-- =============================================
-- 初始化数据
-- =============================================

-- 插入默认分类
INSERT INTO `doc_category` (`id`, `parent_id`, `name`, `description`, `icon`, `sort`, `status`, `creator_id`, `create_time`, `update_time`, `tenant_id`, `deleted`) VALUES
(1, 0, '技术文档', '技术相关的文档资料', 'ep:document', 1, 1, 1, NOW(), NOW(), 1, 0),
(2, 0, '产品文档', '产品设计和需求文档', 'ep:folder', 2, 1, 1, NOW(), NOW(), 1, 0),
(3, 0, '管理制度', '公司管理制度和流程', 'ep:files', 3, 1, 1, NOW(), NOW(), 1, 0),
(4, 1, '开发规范', '代码开发规范和标准', 'ep:document-copy', 1, 1, 1, NOW(), NOW(), 1, 0),
(5, 1, 'API文档', 'API接口文档', 'ep:connection', 2, 1, 1, NOW(), NOW(), 1, 0);

-- 插入默认标签
INSERT INTO `doc_tag` (`id`, `name`, `color`, `description`, `use_count`, `creator_id`, `create_time`, `update_time`, `tenant_id`, `deleted`) VALUES
(1, 'Java', '#f56c6c', 'Java相关技术', 0, 1, NOW(), NOW(), 1, 0),
(2, 'Vue', '#67c23a', 'Vue.js前端框架', 0, 1, NOW(), NOW(), 1, 0),
(3, 'Spring Boot', '#409eff', 'Spring Boot框架', 0, 1, NOW(), NOW(), 1, 0),
(4, '数据库', '#909399', '数据库相关', 0, 1, NOW(), NOW(), 1, 0),
(5, '部署', '#e6a23c', '部署运维相关', 0, 1, NOW(), NOW(), 1, 0);

-- =============================================
-- 菜单权限配置
-- =============================================

-- 文档管理主菜单
INSERT INTO `system_menu` (`name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`, `component`, `component_name`, `status`, `visible`, `keep_alive`, `always_show`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) VALUES
('Document Management', '', 1, 50, 0, '/document', 'ep:document', NULL, NULL, 0, 1, 1, 1, '1', NOW(), '1', NOW(), 0);

-- 获取刚插入的文档管理菜单ID（假设为最新的ID）
SET @doc_menu_id = LAST_INSERT_ID();

-- 文档管理子菜单
INSERT INTO `system_menu` (`name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`, `component`, `component_name`, `status`, `visible`, `keep_alive`, `always_show`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) VALUES
('知识库', 'document:knowledge:query', 2, 1, @doc_menu_id, 'knowledge', 'ep:folder-opened', 'document/knowledge/index', 'DocumentKnowledge', 0, 1, 1, 1, '1', NOW(), '1', NOW(), 0),
('文档列表', 'document:list:query', 2, 2, @doc_menu_id, 'list', 'ep:document-copy', 'document/list/index', 'DocumentList', 0, 1, 1, 1, '1', NOW(), '1', NOW(), 0),
('分类管理', 'document:category:query', 2, 3, @doc_menu_id, 'category', 'ep:folder', 'document/category/index', 'DocumentCategory', 0, 1, 1, 1, '1', NOW(), '1', NOW(), 0),
('标签管理', 'document:tag:query', 2, 4, @doc_menu_id, 'tag', 'ep:collection-tag', 'document/tag/index', 'DocumentTag', 0, 1, 1, 1, '1', NOW(), '1', NOW(), 0);

-- 文档操作按钮权限
INSERT INTO `system_menu` (`name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`, `component`, `component_name`, `status`, `visible`, `keep_alive`, `always_show`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) VALUES
-- 文档基础操作
('文档查询', 'document:query', 3, 1, @doc_menu_id + 2, '', '', '', '', 0, 1, 1, 1, '1', NOW(), '1', NOW(), 0),
('文档创建', 'document:create', 3, 2, @doc_menu_id + 2, '', '', '', '', 0, 1, 1, 1, '1', NOW(), '1', NOW(), 0),
('文档更新', 'document:update', 3, 3, @doc_menu_id + 2, '', '', '', '', 0, 1, 1, 1, '1', NOW(), '1', NOW(), 0),
('文档删除', 'document:delete', 3, 4, @doc_menu_id + 2, '', '', '', '', 0, 1, 1, 1, '1', NOW(), '1', NOW(), 0),
('文档导出', 'document:export', 3, 5, @doc_menu_id + 2, '', '', '', '', 0, 1, 1, 1, '1', NOW(), '1', NOW(), 0),
-- 分类操作
('分类创建', 'document:category:create', 3, 1, @doc_menu_id + 3, '', '', '', '', 0, 1, 1, 1, '1', NOW(), '1', NOW(), 0),
('分类更新', 'document:category:update', 3, 2, @doc_menu_id + 3, '', '', '', '', 0, 1, 1, 1, '1', NOW(), '1', NOW(), 0),
('分类删除', 'document:category:delete', 3, 3, @doc_menu_id + 3, '', '', '', '', 0, 1, 1, 1, '1', NOW(), '1', NOW(), 0),
-- 标签操作
('标签创建', 'document:tag:create', 3, 1, @doc_menu_id + 4, '', '', '', '', 0, 1, 1, 1, '1', NOW(), '1', NOW(), 0),
('标签更新', 'document:tag:update', 3, 2, @doc_menu_id + 4, '', '', '', '', 0, 1, 1, 1, '1', NOW(), '1', NOW(), 0),
('标签删除', 'document:tag:delete', 3, 3, @doc_menu_id + 4, '', '', '', '', 0, 1, 1, 1, '1', NOW(), '1', NOW(), 0);

-- 恢复设置
SET FOREIGN_KEY_CHECKS = 1;
